#!/usr/bin/env python3
"""
اختبار التكامل الجديد لسحب القنوات الخاصة داخل البوت الرئيسي
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

async def test_telethon_client():
    """اختبار إنشاء عميل Telethon"""
    try:
        from telethon import TelegramClient
        
        API_ID = int(os.getenv("API_ID"))
        API_HASH = os.getenv("API_HASH")
        BOT_TOKEN = os.getenv("BOT_TOKEN")
        
        print("🧪 اختبار إنشاء عميل Telethon...")
        
        client = TelegramClient('test_session', API_ID, API_HASH)
        await client.start(bot_token=BOT_TOKEN)
        
        print("✅ تم إنشاء عميل Telethon بنجاح!")
        
        # اختبار الحصول على معلومات البوت
        me = await client.get_me()
        print(f"✅ معلومات البوت: @{me.username}")
        
        await client.disconnect()
        print("✅ تم قطع الاتصال بنجاح")
        
        # حذف ملف الجلسة المؤقت
        try:
            os.remove('test_session.session')
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Telethon: {str(e)}")
        return False

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    print("🧪 اختبار استيراد المكتبات...")
    
    try:
        from telethon import TelegramClient
        from telethon.tl.types import DocumentAttributeFilename, MessageMediaDocument
        print("✅ تم استيراد Telethon بنجاح")
        
        from pyrogram import Client, filters, enums
        print("✅ تم استيراد Pyrogram بنجاح")
        
        import re
        import os
        import time
        import asyncio
        print("✅ تم استيراد المكتبات الأساسية بنجاح")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {str(e)}")
        return False

def test_environment_variables():
    """اختبار متغيرات البيئة"""
    print("🧪 اختبار متغيرات البيئة...")
    
    required_vars = ["API_ID", "API_HASH", "BOT_TOKEN"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: موجود")
        else:
            print(f"❌ {var}: مفقود")
            return False
    
    return True

def test_regex_patterns():
    """اختبار أنماط التعبيرات النمطية"""
    print("🧪 اختبار أنماط التعبيرات النمطية...")
    
    import re
    
    # اختبار نمط القنوات الخاصة
    private_pattern = r"https://t\.me/(?:c/)?([^/]+)"
    
    test_links = [
        "https://t.me/c/1234567890",
        "https://t.me/channel_name",
        "https://t.me/c/1234567890/123"
    ]
    
    for link in test_links:
        match = re.match(private_pattern, link)
        if match:
            print(f"✅ نمط صحيح: {link} -> {match.group(1)}")
        else:
            print(f"❌ نمط خاطئ: {link}")
    
    return True

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار التكامل الجديد للقنوات الخاصة")
    print("=" * 60)
    
    # اختبار استيراد المكتبات
    if not test_imports():
        print("❌ فشل في اختبار المكتبات")
        return
    
    # اختبار متغيرات البيئة
    if not test_environment_variables():
        print("❌ فشل في اختبار متغيرات البيئة")
        return
    
    # اختبار أنماط التعبيرات النمطية
    if not test_regex_patterns():
        print("❌ فشل في اختبار الأنماط")
        return
    
    # اختبار عميل Telethon
    if not await test_telethon_client():
        print("❌ فشل في اختبار عميل Telethon")
        return
    
    print("=" * 60)
    print("✅ جميع الاختبارات نجحت!")
    print("🎉 التكامل الجديد للقنوات الخاصة جاهز للاستخدام")
    print("\n📋 الميزات الجديدة:")
    print("• ✅ سحب من القنوات الخاصة مباشرة داخل البوت")
    print("• ✅ لا حاجة لتشغيل بوت منفصل")
    print("• ✅ واجهة موحدة للمستخدم")
    print("• ✅ معالجة أفضل للأخطاء")

if __name__ == "__main__":
    asyncio.run(main())
