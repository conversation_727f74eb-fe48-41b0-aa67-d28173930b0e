from telethon import TelegramClient, events
from telethon.tl.types import DocumentAttributeFilename, MessageMediaDocument
import asyncio
import os
import re
import time

# إعدادات البوت
API_ID = 23721585
API_HASH = "31e72861ba87173e43baeb0232a595aa"
BOT_TOKEN = "7491002839:AAFe97o_wTLdQ-Z3rwKro2NFJ1TV_mrCMT8"  # بدلاً من رقم الهاتف

# إنشاء مجلد للتحميلات المؤقتة
TEMP_DOWNLOAD_DIR = "temp_downloads"
if not os.path.exists(TEMP_DOWNLOAD_DIR):
    os.makedirs(TEMP_DOWNLOAD_DIR)

# إنشاء العميل - بوت عام
client = TelegramClient('public_bot_session', API_ID, API_HASH)

async def copy_channel_messages(event, channel_link, limit=None):
    try:
        # استخراج معرف القناة من الرابط
        match = re.match(r"https://t\.me/(?:c/)?([^/]+)", channel_link)
        if not match:
            await event.reply("❌ رابط القناة غير صحيح!")
            return

        channel = match.group(1)
        if channel.isdigit():
            channel = int(f"-100{channel}")

        if limit is None:
            limit = 100
        
        await event.reply(f"🔄 جاري نسخ آخر {limit} رسالة...")
        
        messages = await client.get_messages(channel, limit=limit, filter=None)
        
        if not messages:
            await event.reply("⚠️ لم يتم العثور على رسائل!")
            return

        copied_count = 0
        failed_count = 0

        for msg in messages[::-1]:
            try:
                if hasattr(msg, 'media') and isinstance(msg.media, MessageMediaDocument):
                    file_name = ""
                    for attr in msg.media.document.attributes:
                        if isinstance(attr, DocumentAttributeFilename):
                            file_name = attr.file_name
                            break
                    
                    if not file_name:
                        file_name = f"file_{int(time.time())}"

                    print(f"⬇️ جاري تحميل الملف: {file_name}")
                    
                    download_path = os.path.join(TEMP_DOWNLOAD_DIR, file_name)
                    await msg.download_media(download_path)
                    
                    print(f"⬆️ جاري إعادة رفع الملف: {file_name}")
                    
                    await client.send_file(
                        event.chat_id,
                        download_path,
                        caption=msg.text if msg.text else None,
                        force_document=True
                    )
                    
                    try:
                        os.remove(download_path)
                    except:
                        pass
                    
                else:
                    await client.send_message(
                        event.chat_id,
                        message=msg,
                        formatting_entities=msg.entities
                    )
                
                copied_count += 1
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"❌ خطأ في نسخ رسالة: {str(e)}")
                failed_count += 1
                continue

        try:
            os.rmdir(TEMP_DOWNLOAD_DIR)
        except:
            pass

        await event.reply(
            f"✅ اكتمل النسخ!\n"
            f"📊 الإحصائيات:\n"
            f"✓ تم نسخ: {copied_count} رسالة\n"
            f"❌ فشل نسخ: {failed_count} رسالة"
        )

    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        await event.reply(f"❌ حدث خطأ أثناء نسخ الرسائل: {str(e)}")

async def main():
    # تسجيل الدخول كبوت عام
    await client.start(bot_token=BOT_TOKEN)  # هنا التغيير الرئيسي
    print("✅ تم تسجيل دخول البوت بنجاح!")

    @client.on(events.NewMessage)
    async def handle_messages(event):
        try:
            message = event.message
            text = message.text

            if text.startswith("/start"):
                await event.reply(
                    "🚀 **بوت نسخ الرسائل العام**\n\n"
                    "✨ **الميزات:**\n"
                    "📋 نسخ من القنوات العامة\n"
                    "⚡ معالجة سريعة\n"
                    "📱 دعم جميع أنواع الملفات\n\n"
                    "📖 **الأوامر:**\n"
                    "/copy <رابط> [عدد] - نسخ الرسائل\n\n"
                    "🔗 **مثال:**\n"
                    "`/copy https://t.me/channel_name 50`"
                )
                return

            elif text.startswith("/copy"):
                parts = text.split()
                if len(parts) < 2:
                    await event.reply("❌ الرجاء إدخال رابط القناة")
                    return
                
                channel_link = parts[1]
                limit = int(parts[2]) if len(parts) > 2 else 100
                await copy_channel_messages(event, channel_link, limit)

            elif 't.me/' in text:
                parts = text.split('/')
                channel = parts[-2]
                message_id = int(parts[-1])
                
                try:
                    if channel.isdigit():
                        channel = int(f"-100{channel}")
                    
                    msg = await client.get_messages(channel, ids=message_id)
                    if msg:
                        await client.send_message(
                            event.chat_id,
                            message=msg,
                            formatting_entities=msg.entities
                        )
                    else:
                        await event.reply("❌ لم يتم العثور على الرسالة")
                        
                except Exception as e:
                    await event.reply("❌ حدث خطأ في جلب المحتوى")

        except Exception as e:
            print(f"خطأ: {str(e)}")
            await event.reply("❌ حدث خطأ في معالجة الطلب")

    print("👂 البوت العام يعمل ويستمع للأوامر...")
    await client.run_until_disconnected()

if __name__ == '__main__':
    asyncio.run(main())