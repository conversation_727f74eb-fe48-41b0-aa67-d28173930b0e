#!/usr/bin/env python3
"""
اختبار بسيط للتأكد من أن التكامل بين bot.py و bot2.py يعمل بشكل صحيح
"""

import subprocess
import sys
import os
import time

def test_bot2_launch():
    """اختبار تشغيل bot2.py"""
    print("🧪 اختبار تشغيل bot2.py...")
    
    try:
        # محاولة تشغيل bot2.py
        process = subprocess.Popen([sys.executable, "bot2.py"], 
                                 cwd=os.getcwd(),
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        print(f"✅ تم تشغيل bot2.py بنجاح! Process ID: {process.pid}")
        
        # انتظار قليل للتأكد من أن العملية بدأت
        time.sleep(2)
        
        # التحقق من حالة العملية
        if process.poll() is None:
            print("✅ bot2.py يعمل بشكل طبيعي")
            
            # إيقاف العملية
            process.terminate()
            process.wait(timeout=5)
            print("✅ تم إيقاف bot2.py بنجاح")
            
            return True
        else:
            print("❌ bot2.py توقف بشكل غير متوقع")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل bot2.py: {str(e)}")
        return False

def test_file_existence():
    """التحقق من وجود الملفات المطلوبة"""
    print("🧪 التحقق من وجود الملفات...")
    
    files_to_check = ["bot.py", "bot2.py"]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            print(f"✅ {file_name} موجود")
        else:
            print(f"❌ {file_name} غير موجود")
            return False
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار التكامل بين البوتات")
    print("=" * 50)
    
    # اختبار وجود الملفات
    if not test_file_existence():
        print("❌ فشل في اختبار وجود الملفات")
        return
    
    # اختبار تشغيل bot2.py
    if not test_bot2_launch():
        print("❌ فشل في اختبار تشغيل bot2.py")
        return
    
    print("=" * 50)
    print("✅ جميع الاختبارات نجحت!")
    print("🎉 التكامل بين البوتات يعمل بشكل صحيح")

if __name__ == "__main__":
    main()
